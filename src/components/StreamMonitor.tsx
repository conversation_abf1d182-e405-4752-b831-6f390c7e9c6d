'use client';

import React, { useState, useEffect } from 'react';

interface StreamStats {
  totalStreams: number;
  totalClients: number;
  streamDetails: Array<{
    id: string;
    name: string;
    rtspUrl: string;
    status: string;
    clientCount: number;
    uptime: number;
    lastClientDisconnect?: number;
    lastAccess?: number;
  }>;
}

interface MonitorData {
  websocketStats: StreamStats;
  hlsStats: StreamStats;
}

export default function StreamMonitor({ onClose }: { onClose: () => void }) {
  const [data, setData] = useState<MonitorData | null>(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchStats = async () => {
    try {
      const [wsResponse, hlsResponse] = await Promise.all([
        fetch('/api/stream-stats'),
        fetch('/api/hls-stream-stats')
      ]);

      const wsData = await wsResponse.json();
      const hlsData = await hlsResponse.json();

      setData({
        websocketStats: wsData.success ? wsData.stats : { totalStreams: 0, totalClients: 0, streamDetails: [] },
        hlsStats: hlsData.success ? hlsData.stats : { totalStreams: 0, totalClients: 0, streamDetails: [] }
      });
    } catch (error) {
      console.error('Failed to fetch stream stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    
    let interval: NodeJS.Timeout | null = null;
    if (autoRefresh) {
      interval = setInterval(fetchStats, 5000); // Refresh every 5 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const formatUptime = (uptime: number) => {
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatLastActivity = (timestamp?: number) => {
    if (!timestamp) return 'N/A';
    const ago = Date.now() - timestamp;
    return `${Math.floor(ago / 1000)}s ago`;
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-center">Loading stream statistics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Stream Performance Monitor</h2>
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm">Auto-refresh (5s)</span>
              </label>
              <button
                onClick={fetchStats}
                className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Refresh
              </button>
              <button
                onClick={onClose}
                className="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-800">WebSocket Streams</h3>
              <p className="text-2xl font-bold text-blue-600">{data?.websocketStats.totalStreams || 0}</p>
              <p className="text-sm text-blue-600">{data?.websocketStats.totalClients || 0} clients</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-800">HLS Streams</h3>
              <p className="text-2xl font-bold text-green-600">{data?.hlsStats.totalStreams || 0}</p>
              <p className="text-sm text-green-600">{data?.hlsStats.totalClients || 0} clients</p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-semibold text-purple-800">Total Streams</h3>
              <p className="text-2xl font-bold text-purple-600">
                {(data?.websocketStats.totalStreams || 0) + (data?.hlsStats.totalStreams || 0)}
              </p>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <h3 className="font-semibold text-orange-800">Total Clients</h3>
              <p className="text-2xl font-bold text-orange-600">
                {(data?.websocketStats.totalClients || 0) + (data?.hlsStats.totalClients || 0)}
              </p>
            </div>
          </div>

          {/* WebSocket Streams */}
          <div className="mb-8">
            <h3 className="text-xl font-bold mb-4">WebSocket Streams</h3>
            {data?.websocketStats.streamDetails.length ? (
              <div className="overflow-x-auto">
                <table className="w-full border border-gray-200 rounded-lg">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="p-3 text-left">Name</th>
                      <th className="p-3 text-left">Status</th>
                      <th className="p-3 text-left">Clients</th>
                      <th className="p-3 text-left">Uptime</th>
                      <th className="p-3 text-left">Last Activity</th>
                      <th className="p-3 text-left">RTSP URL</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.websocketStats.streamDetails.map((stream) => (
                      <tr key={stream.id} className="border-t border-gray-200">
                        <td className="p-3 font-medium">{stream.name}</td>
                        <td className="p-3">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            stream.status === 'running' ? 'bg-green-100 text-green-800' :
                            stream.status === 'starting' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {stream.status}
                          </span>
                        </td>
                        <td className="p-3">{stream.clientCount}</td>
                        <td className="p-3">{formatUptime(stream.uptime)}</td>
                        <td className="p-3">{formatLastActivity(stream.lastClientDisconnect)}</td>
                        <td className="p-3 text-xs text-gray-600 max-w-xs truncate">{stream.rtspUrl}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No WebSocket streams active</p>
            )}
          </div>

          {/* HLS Streams */}
          <div>
            <h3 className="text-xl font-bold mb-4">HLS Streams</h3>
            {data?.hlsStats.streamDetails.length ? (
              <div className="overflow-x-auto">
                <table className="w-full border border-gray-200 rounded-lg">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="p-3 text-left">Name</th>
                      <th className="p-3 text-left">Status</th>
                      <th className="p-3 text-left">Clients</th>
                      <th className="p-3 text-left">Uptime</th>
                      <th className="p-3 text-left">Last Access</th>
                      <th className="p-3 text-left">RTSP URL</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.hlsStats.streamDetails.map((stream) => (
                      <tr key={stream.id} className="border-t border-gray-200">
                        <td className="p-3 font-medium">{stream.name}</td>
                        <td className="p-3">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            stream.status === 'running' ? 'bg-green-100 text-green-800' :
                            stream.status === 'starting' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {stream.status}
                          </span>
                        </td>
                        <td className="p-3">{stream.clientCount}</td>
                        <td className="p-3">{formatUptime(stream.uptime)}</td>
                        <td className="p-3">{formatLastActivity(stream.lastAccess)}</td>
                        <td className="p-3 text-xs text-gray-600 max-w-xs truncate">{stream.rtspUrl}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No HLS streams active</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
